//
//  MyWidgetSmallView.swift
//  MyWidgetExtensionExtension
//
//  Created by lifubing on 2025/9/21.
//

import WidgetKit
import SwiftUI
import AppIntents


struct MyWidgetSmallView: View {
    var entry: PhotoEntry
    
    var body: some View {
        
        GeometryReader { geometry in
            
            let sizeMax = 1.3
            
            let offsetSize_X = entry.isResized ? 0 : 0.14
            let offsetSize_Y = entry.isResized ? -0.08 : 0.1
            
            let sizeForHeight = entry.isResized ? 1 : 1.1
            
            let fullWidth = entry.isResized ? geometry.size.width : geometry.size.width * 1.1
            
            ZStack {
                // 背景图片或渐变，完全填满Widget
                if let photo = entry.photo, let photoIdentifier = entry.photoIdentifier, !photoIdentifier.isEmpty {
                    
                    if entry.isResized,
                       let photo = entry.photo {
                            Image(uiImage: photo)
                                .aspectRatio(contentMode: .fill) // 让背景图片填满视图
                                .frame(width: geometry.size.width, height: geometry.size.height)
                                .blur(radius: 7) // 调整模糊程度
                    }
                    
                    let resizedPhotoWidth = geometry.size.width * 1.1
                    let resizedPhotoHeight = resizedPhotoWidth * photo.size.height / photo.size.width

                    // 计算调整后的高度，确保图片不会超出容器高度，同时保持宽高比
                    let finalPhotoHeight = min(resizedPhotoHeight, geometry.size.height)
                    let finalPhotoWidth = finalPhotoHeight * photo.size.width / photo.size.height

                    // 如果计算后的宽度超出容器宽度，就调整宽度到最大宽度，并重新计算高度
                    let finalResizedPhotoWidth = min(finalPhotoWidth, geometry.size.width)
                    let finalResizedPhotoHeight = finalResizedPhotoWidth * photo.size.height / photo.size.width
                    
                    if entry.isResized {
                        HStack(spacing: 0) {
                            // 左侧：完整照片显示区域
                            Link(destination: URL(string: "photoclear://openPhoto?id=\(photoIdentifier)")!) {
                                Image(uiImage: photo)
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                                    .frame(width: finalResizedPhotoWidth, height: finalResizedPhotoHeight)
                                    .clipped()
                                    .cornerRadius(10)
//                                    .padding(2)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 10)
                                            .stroke(Color.white, lineWidth: 2)
                                    )
                            }
                        }
                        .offset(y:-6)
                    } else {
                        // 正常模式：平铺显示
                        Link(destination: URL(string: "photoclear://openPhoto?id=\(photoIdentifier)")!) {
                            Image(uiImage: photo)
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                                .frame(width: geometry.size.width * sizeMax, height: geometry.size.height * sizeMax)
                                .offset(x: -geometry.size.width * offsetSize_X, y: -geometry.size.height * offsetSize_X)
                        }
                    }
                    
                } else {
                    // 默认背景
                    Link(destination: URL(string: "photoclear://openPhoto")!) {
                        Text("所有照片都已操作过，请点击打开APP更新缓存")
                            .font(.system(size: 12))
                            .multilineTextAlignment(.center)
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                    }
                }
                
                let resizeBtnOffset_Y = entry.isResized ? 0 : 6.0
                let resizeBtnOffset_X = entry.isResized ? 0 : 8.0
                
                // 按钮区域 - 放在右上角
                VStack {
                    HStack {
                        Spacer()
                        if let photoIdentifier = entry.photoIdentifier {
                            if entry.isResized {
                                Button(intent: UnResizePhotoIntent(photoIdentifier:photoIdentifier)) {
                                    Image(systemName: "arrow.up.left.and.arrow.down.right")
                                        .font(.system(size: 14))
                                        .foregroundColor(.white)
                                        .frame(width: 32, height: 32)
                                        .shadow(color: .black, radius: 8)
                                        
                                }
                                .buttonStyle(PlainButtonStyle())
                                
                            } else {
                                Button(intent: ResizePhotoIntent(photoIdentifier:photoIdentifier)) {
                                    Image(systemName: "arrow.up.right.and.arrow.down.left")
                                        .font(.system(size: 14))
                                        .foregroundColor(.white)
                                        .frame(width: 32, height: 32)
                                        .shadow(color: .black, radius: 8)
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                    }
                    Spacer()
                }
                .frame(width: geometry.size.width * 1, height: geometry.size.height * 1.1)
                .offset(x: -geometry.size.width * offsetSize_X + resizeBtnOffset_X, y: -geometry.size.height * 0.1 - resizeBtnOffset_Y)
                    
                // 按钮区域 - 放在右上角
                VStack {
                    Spacer()
                    HStack(spacing: 8) {
                            
                            if let photoIdentifier = entry.photoIdentifier {
                                Spacer()
                                
                                Button(intent: ArchivePhotoIntent(photoIdentifier: photoIdentifier)) {
                                    Image(systemName: "archivebox")
                                        .font(.system(size: 14))
                                        .bold()
                                        .foregroundColor(.white)
                                        .frame(width: fullWidth / 3, height: 32) // 设置正方形尺寸
                                        .shadow(color: .black, radius: 8)
                                }
                                .buttonStyle(PlainButtonStyle())
                                
                               
                                // 标记删除按钮
                                Button(intent: MarkForDeletionIntent(photoIdentifier: photoIdentifier)) {
                                    Image(systemName: "trash")
                                        .font(.system(size: 14))
                                        .bold()
                                        .foregroundColor(.white)
                                        .frame(width: fullWidth / 3, height: 32) // 设置正方形尺寸
                                        .shadow(color: .black, radius: 8)
                                }
                                .buttonStyle(PlainButtonStyle())
                                
                                Button(intent: NextPhotoIntent(photoIdentifier:photoIdentifier)) {
                                    Image(systemName: "arrow.forward")
                                        .font(.system(size: 15))
                                        .bold()
                                        .foregroundColor(.white)
                                        .frame(width: fullWidth / 3, height: 32) // 设置正方形尺寸
                                        .shadow(color: .black, radius: 8)
                                }
                                .buttonStyle(PlainButtonStyle())
                                
                                Spacer()
                            }
                    }
                }
                .frame(width: geometry.size.width * 1, height: geometry.size.height * sizeForHeight)
                .offset(x: -geometry.size.width * offsetSize_X, y: -geometry.size.height * offsetSize_Y)
            }
        }
    }
}
