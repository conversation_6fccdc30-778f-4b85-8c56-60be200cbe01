//
//  DeleteListViewController.m
//  photoclear
//
//  Created by <PERSON><PERSON> on 2025/9/18.
//

#import "DeleteListViewController.h"
#import "UIView+Extension.h"
#import "Preferences.h"
#import "DeletePhotoCollectionViewCell.h"
#import "WaterfallFlowLayout.h"

@interface DeleteListViewController () <UICollectionViewDelegate, UICollectionViewDataSource, WaterfallFlowLayoutDelegate, DeletePhotoCollectionViewCellDelegate>

@property (nonatomic, strong) UICollectionView *collectionView;
@property (nonatomic, strong) UIButton *confirmButton;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIButton *backButton;
@property (nonatomic, strong) NSMutableArray<PHAsset *> *deleteAssets;
@property (nonatomic, strong) WaterfallFlowLayout *waterfallLayout;

// 全屏浏览相关属性
@property (nonatomic, strong) UIView *fullScreenView;
@property (nonatomic, strong) UIImageView *fullScreenImageView;
@property (nonatomic, strong) UITapGestureRecognizer *fullScreenTapGesture;

@end

@implementation DeleteListViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.view.backgroundColor = [UIColor blackColor];
    
    [self setupUI];
    [self loadDeleteAssets];
}

- (void)setupUI {
    // 返回按钮
    if (!self.tabBarController) {
        self.backButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [self.backButton setTitle:@"返回" forState:UIControlStateNormal];
        [self.backButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        self.backButton.titleLabel.font = [UIFont systemFontOfSize:16];
        [self.backButton addTarget:self action:@selector(backButtonTapped) forControlEvents:UIControlEventTouchUpInside];
        [self.view addSubview:self.backButton];
    }
    
    // 标题
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.text = [NSString stringWithFormat:@"待删除照片 (%ld)", self.deletePhotoIdentifiers.count];
    self.titleLabel.textColor = [UIColor whiteColor];
    self.titleLabel.font = [UIFont systemFontOfSize:15];
    self.titleLabel.textAlignment = NSTextAlignmentCenter;
    [self.view addSubview:self.titleLabel];
    
    // 瀑布流布局
    self.waterfallLayout = [[WaterfallFlowLayout alloc] init];
    self.waterfallLayout.delegate = self;
    
    // 集合视图
    self.collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:self.waterfallLayout];
    self.collectionView.backgroundColor = [UIColor blackColor];
    self.collectionView.delegate = self;
    self.collectionView.dataSource = self;
    self.collectionView.showsVerticalScrollIndicator = YES;
    self.collectionView.alwaysBounceVertical = YES;
    [self.collectionView registerClass:[DeletePhotoCollectionViewCell class] forCellWithReuseIdentifier:@"DeletePhotoCell"];
    [self.view addSubview:self.collectionView];
    
    // 确认删除按钮
    self.confirmButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.confirmButton setTitle:@"确认删除" forState:UIControlStateNormal];
    [self.confirmButton setBackgroundColor:[UIColor redColor]];
    [self.confirmButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.confirmButton.titleLabel.font = [UIFont boldSystemFontOfSize:16];
    self.confirmButton.layer.cornerRadius = 8;
    [self.confirmButton addTarget:self action:@selector(confirmDeleteTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.confirmButton];
    
    [self setupConstraints];
}

- (void)setupConstraints {
    // 禁用自动转换为约束
    if (self.backButton) {
        self.backButton.translatesAutoresizingMaskIntoConstraints = NO;
    }
    self.titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    self.confirmButton.translatesAutoresizingMaskIntoConstraints = NO;
    self.collectionView.translatesAutoresizingMaskIntoConstraints = NO;
    
    NSMutableArray *constraints = [NSMutableArray array];
    
    // 返回按钮约束（如果存在）
    if (self.backButton) {
        [constraints addObjectsFromArray:@[
            [self.backButton.leadingAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.leadingAnchor constant:20],
            [self.backButton.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor constant:10],
            [self.backButton.widthAnchor constraintEqualToConstant:60],
            [self.backButton.heightAnchor constraintEqualToConstant:30]
        ]];
    }
    
    // 标题约束
    [constraints addObjectsFromArray:@[
        [self.titleLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.titleLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.titleLabel.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor constant:8],
        [self.titleLabel.heightAnchor constraintEqualToConstant:24]
    ]];
    
    // 确认删除按钮约束
    [constraints addObjectsFromArray:@[
        [self.confirmButton.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:20],
        [self.confirmButton.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-20],
        [self.confirmButton.heightAnchor constraintEqualToConstant:50]
    ]];
    
    // 根据是否有 tabBarController 设置底部约束
    if (self.tabBarController) {
        [constraints addObject:[self.confirmButton.bottomAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.bottomAnchor constant:-10]];
    } else {
        [constraints addObject:[self.confirmButton.bottomAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.bottomAnchor constant:-10]];
    }
    
    // 集合视图约束
    [constraints addObjectsFromArray:@[
        [self.collectionView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.collectionView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.collectionView.topAnchor constraintEqualToAnchor:self.titleLabel.bottomAnchor constant:10],
        [self.collectionView.bottomAnchor constraintEqualToAnchor:self.confirmButton.topAnchor constant:-10]
    ]];
    
    // 激活所有约束
    [NSLayoutConstraint activateConstraints:constraints];
}

- (void)loadDeleteAssets {
    if (self.deletePhotoIdentifiers.count == 0) {
//        self.deleteAssets = [NSMutableArray array];
        self.deletePhotoIdentifiers = [[NSMutableArray alloc] initWithArray:[Preferences sharedInstance].deleteArray];
//        return;
    }
    
    PHFetchResult<PHAsset *> *assets = [PHAsset fetchAssetsWithLocalIdentifiers:self.deletePhotoIdentifiers options:nil];
    self.deleteAssets = [NSMutableArray arrayWithArray:[assets objectsAtIndexes:[NSIndexSet indexSetWithIndexesInRange:NSMakeRange(0, assets.count)]]];
    
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.collectionView reloadData];
        self.titleLabel.text = [NSString stringWithFormat:@"待删除照片 (%ld)", self.deleteAssets.count];
    });
}

#pragma mark - UICollectionViewDataSource

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.deleteAssets.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    DeletePhotoCollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"DeletePhotoCell" forIndexPath:indexPath];
    
    PHAsset *asset = self.deleteAssets[indexPath.item];
    cell.delegate = self;
    cell.cellIndex = indexPath.item;
    [cell configureWithAsset:asset];
    
    return cell;
}

#pragma mark - UICollectionViewDelegate

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    [collectionView deselectItemAtIndexPath:indexPath animated:YES];

    PHAsset *asset = self.deleteAssets[indexPath.item];
    [self showFullScreenImageWithAsset:asset];
}

#pragma mark - WaterfallFlowLayoutDelegate

- (CGFloat)waterfallFlowLayout:(UICollectionViewLayout *)layout heightForItemAtIndexPath:(NSIndexPath *)indexPath itemWidth:(CGFloat)itemWidth {
    PHAsset *asset = self.deleteAssets[indexPath.item];
    
    // 根据照片的宽高比计算高度
    CGFloat aspectRatio = (CGFloat)asset.pixelHeight / (CGFloat)asset.pixelWidth;
    CGFloat itemHeight = itemWidth * aspectRatio;
    
    // 限制最小和最大高度
    CGFloat minHeight = itemWidth * 0.6; // 最小高度为宽度的60%
    CGFloat maxHeight = itemWidth * 2.0; // 最大高度为宽度的200%
    
    return MAX(minHeight, MIN(maxHeight, itemHeight));
}

- (NSInteger)numberOfColumnsInWaterfallFlowLayout:(UICollectionViewLayout *)layout {
    // 固定显示3列
    return 3;
}

- (CGFloat)columnSpacingInWaterfallFlowLayout:(UICollectionViewLayout *)layout {
    return 8;
}

- (CGFloat)rowSpacingInWaterfallFlowLayout:(UICollectionViewLayout *)layout {
    return 8;
}

- (UIEdgeInsets)edgeInsetsInWaterfallFlowLayout:(UICollectionViewLayout *)layout {
    return UIEdgeInsetsMake(10, 10, 10, 10);
}

#pragma mark - DeletePhotoCollectionViewCellDelegate

- (void)deletePhotoCell:(UICollectionViewCell *)cell didTapRemoveButtonAtIndex:(NSInteger)index {
    [self removePhotoAtIndex:index];
}

#pragma mark - Actions

- (void)backButtonTapped {
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)removePhotoAtIndex:(NSInteger)index {
    if (index < self.deleteAssets.count) {
        PHAsset *asset = self.deleteAssets[index];
        NSString *removedIdentifier = asset.localIdentifier;
        NSString *toRemovedIdentifier = @"";
        for (NSString *photoIdentifier in self.deletePhotoIdentifiers) {
            if ([removedIdentifier containsString:photoIdentifier]) {
                toRemovedIdentifier = photoIdentifier;
            }
        }
        
        
        // 从数组中移除
        [self.deletePhotoIdentifiers removeObject:toRemovedIdentifier];
        [self.deleteAssets removeObjectAtIndex:index];
        
        // 同步更新 Preferences 中的 deleteArray
        [Preferences sharedInstance].deleteArray = [self.deletePhotoIdentifiers copy];
        
//        // 通知代理照片被移除
//        if (self.delegate && [self.delegate respondsToSelector:@selector(deleteListDidRemovePhoto:)]) {
//            [self.delegate deleteListDidRemovePhoto:removedIdentifier];
//        }

        // 更新UI
        NSIndexPath *indexPath = [NSIndexPath indexPathForItem:index inSection:0];
        [self.collectionView deleteItemsAtIndexPaths:@[indexPath]];
        
        self.titleLabel.text = [NSString stringWithFormat:@"待删除照片 (%ld)", self.deleteAssets.count];
        
        // 重新布局
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self.collectionView reloadData];
        });
    }
}

#pragma mark - Full Screen Image View

- (void)showFullScreenImageWithAsset:(PHAsset *)asset {
    // 创建全屏视图
    self.fullScreenView = [[UIView alloc] initWithFrame:self.view.bounds];
    self.fullScreenView.backgroundColor = [UIColor blackColor];
    self.fullScreenView.alpha = 0;

    // 创建图片视图
    self.fullScreenImageView = [[UIImageView alloc] init];
    self.fullScreenImageView.contentMode = UIViewContentModeScaleAspectFit;
    self.fullScreenImageView.userInteractionEnabled = YES;
    [self.fullScreenView addSubview:self.fullScreenImageView];

    // 添加点击手势
    self.fullScreenTapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(hideFullScreenImage)];
    [self.fullScreenImageView addGestureRecognizer:self.fullScreenTapGesture];

    // 设置约束
    self.fullScreenImageView.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [self.fullScreenImageView.centerXAnchor constraintEqualToAnchor:self.fullScreenView.centerXAnchor],
        [self.fullScreenImageView.centerYAnchor constraintEqualToAnchor:self.fullScreenView.centerYAnchor],
        [self.fullScreenImageView.leadingAnchor constraintGreaterThanOrEqualToAnchor:self.fullScreenView.leadingAnchor constant:20],
        [self.fullScreenImageView.trailingAnchor constraintLessThanOrEqualToAnchor:self.fullScreenView.trailingAnchor constant:-20],
        [self.fullScreenImageView.topAnchor constraintGreaterThanOrEqualToAnchor:self.fullScreenView.safeAreaLayoutGuide.topAnchor constant:20],
        [self.fullScreenImageView.bottomAnchor constraintLessThanOrEqualToAnchor:self.fullScreenView.safeAreaLayoutGuide.bottomAnchor constant:-20]
    ]];

    // 添加到主视图
    [self.view addSubview:self.fullScreenView];

    // 请求高质量图片
    PHImageRequestOptions *options = [[PHImageRequestOptions alloc] init];
    options.deliveryMode = PHImageRequestOptionsDeliveryModeHighQualityFormat;
    options.resizeMode = PHImageRequestOptionsResizeModeExact;
    options.synchronous = NO;

    CGSize targetSize = CGSizeMake(self.view.bounds.size.width * 2, self.view.bounds.size.height * 2);

    [[PHImageManager defaultManager] requestImageForAsset:asset
                                               targetSize:targetSize
                                              contentMode:PHImageContentModeAspectFit
                                                  options:options
                                            resultHandler:^(UIImage * _Nullable result, NSDictionary * _Nullable info) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (result && self.fullScreenImageView) {
                self.fullScreenImageView.image = result;

                // 动画显示
                [UIView animateWithDuration:0.3 animations:^{
                    self.fullScreenView.alpha = 1.0;
                }];
            }
        });
    }];
}

- (void)hideFullScreenImage {
    if (self.fullScreenView) {
        [UIView animateWithDuration:0.3 animations:^{
            self.fullScreenView.alpha = 0;
        } completion:^(BOOL finished) {
            [self.fullScreenView removeFromSuperview];
            self.fullScreenView = nil;
            self.fullScreenImageView = nil;
            self.fullScreenTapGesture = nil;
        }];
    }
}

- (void)confirmDeleteTapped {
    if (self.deleteAssets.count == 0) {
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"提示" message:@"没有要删除的照片" preferredStyle:UIAlertControllerStyleAlert];
        [alert addAction:[UIAlertAction actionWithTitle:@"确定" style:UIAlertActionStyleDefault handler:nil]];
        [self presentViewController:alert animated:YES completion:nil];
        return;
    }
    
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"确认删除" 
                                                                   message:[NSString stringWithFormat:@"确定要删除这 %ld 张照片吗？此操作不可撤销。", self.deleteAssets.count] 
                                                            preferredStyle:UIAlertControllerStyleAlert];
    
    [alert addAction:[UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleCancel handler:nil]];
    
    [alert addAction:[UIAlertAction actionWithTitle:@"删除" style:UIAlertActionStyleDestructive handler:^(UIAlertAction * _Nonnull action) {
        [self performDeletion];
    }]];
    
    [self presentViewController:alert animated:YES completion:nil];
}

- (void)performDeletion {
    if (self.deleteAssets.count == 0) return;
    
    // 显示加载指示器
    UIAlertController *loadingAlert = [UIAlertController alertControllerWithTitle:@"删除中..." message:@"请稍候" preferredStyle:UIAlertControllerStyleAlert];
    [self presentViewController:loadingAlert animated:YES completion:nil];
    
    [[PHPhotoLibrary sharedPhotoLibrary] performChanges:^{
        [PHAssetChangeRequest deleteAssets:self.deleteAssets];
    } completionHandler:^(BOOL success, NSError * _Nullable error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [loadingAlert dismissViewControllerAnimated:YES completion:^{
                if (success) {
                    // 通知代理删除成功
                    if (self.delegate && [self.delegate respondsToSelector:@selector(deleteListDidConfirmDeletion:)]) {
                        [self.delegate deleteListDidConfirmDeletion:[self.deletePhotoIdentifiers copy]];
                    }
                    
                    UIAlertController *successAlert = [UIAlertController alertControllerWithTitle:@"删除成功" message:@"照片已成功删除" preferredStyle:UIAlertControllerStyleAlert];
                    [successAlert addAction:[UIAlertAction actionWithTitle:@"确定" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                        [self dismissViewControllerAnimated:YES completion:nil];
                    }]];
                    [self presentViewController:successAlert animated:YES completion:nil];
                } else {
                    UIAlertController *errorAlert = [UIAlertController alertControllerWithTitle:@"删除失败" message:[NSString stringWithFormat:@"删除失败：%@", error.localizedDescription] preferredStyle:UIAlertControllerStyleAlert];
                    [errorAlert addAction:[UIAlertAction actionWithTitle:@"确定" style:UIAlertActionStyleDefault handler:nil]];
                    [self presentViewController:errorAlert animated:YES completion:nil];
                }
            }];
        });
    }];
}

- (BOOL)prefersStatusBarHidden {
    return YES;
}

@end
