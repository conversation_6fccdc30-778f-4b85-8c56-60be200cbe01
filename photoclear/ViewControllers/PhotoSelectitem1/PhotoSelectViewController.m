//
//  PhotoSelectViewController.m
//  photoclear
//
//  Created by lifubing on 2021/6/13.
//

#import "PhotoSelectViewController.h"
#import "PhotoCollectionViewCell.h"
#import "UIButton+CenterImageAndTitle.h"
#import "ScrollViewController.h"
#import "PhotoTools.h"
#import "AlbumSelectViewController.h"
#import "Preferences.h"

@interface PhotoSelectViewController () <UICollectionViewDelegate, UICollectionViewDataSource, AlbumSelectViewControllerDelegate>
@property (weak, nonatomic) IBOutlet UICollectionView *collectionView;
@property (weak, nonatomic) IBOutlet UIButton *titleBtn;

@property (nonatomic, strong) PHImageRequestOptions *requestOptions;
@property (nonatomic, assign) CGFloat cellwidth;
@property (nonatomic, assign) CGFloat cellMargin;
@property (nonatomic, strong) UIButton *backButton;
@end

@implementation PhotoSelectViewController

- (void)viewDidLoad {
    [super viewDidLoad];

    if (self.fetchResult) {
        [self setupBackButton];
    } else {
        // 如果没有传入 fetchResult，使用默认的所有照片
        self.fetchResult = [PhotoTools allPhotos];
    }

    // 保存原始数据
    self.originalFetchResult = self.fetchResult;

    // 初始化筛选状态
    self.currentFilterType = PhotoFilterTypeUnorganized;

    // 设置标题
    [self updateTitleButton];

    // 创建筛选栏
    [self setupFilterBar];

    self.cellMargin = 2;
    self.cellwidth = floorf((self.view.frame.size.width - self.cellMargin * 2 - 4 * 3) / 4);

    [_collectionView registerClass:[PhotoCollectionViewCell class] forCellWithReuseIdentifier:@"PhotoCollectionViewCell"];
    self.collectionView.delegate = self;
    self.collectionView.dataSource = self;

    self.requestOptions = [[PHImageRequestOptions alloc] init];//请求选项设置
    self.requestOptions.resizeMode = PHImageRequestOptionsResizeModeExact;//自定义图片大小的加载模式
    self.requestOptions.deliveryMode = PHImageRequestOptionsDeliveryModeHighQualityFormat;
    self.requestOptions.synchronous = YES;//是否同步加载

    // 应用初始筛选
    [self applyFilter];
}

- (void)fetchImageWithIndex:(NSInteger)imageIndex withCompletionHandler:(void (^)(PHAsset * phAsset, UIImage *image))completionHandler {
    NSInteger count = self.filteredAssets ? self.filteredAssets.count : self.fetchResult.count;
    if (imageIndex < 0 || imageIndex >= count) {
        return;
    }

    PHAsset *phAsset;
    if (self.filteredAssets) {
        phAsset = self.filteredAssets[imageIndex];
    } else {
        phAsset = [self.fetchResult objectAtIndex:imageIndex];
    }

    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        [[PHImageManager defaultManager] requestImageForAsset:phAsset targetSize:CGSizeMake(self.cellwidth * 1.5, self.cellwidth * 1.5) contentMode:PHImageContentModeAspectFill options:self.requestOptions resultHandler:^(UIImage * _Nullable result, NSDictionary * _Nullable info) {

            dispatch_async(dispatch_get_main_queue(), ^{
                if (completionHandler) {
                    completionHandler(phAsset, result);
                }
            });
        }];
    });
}

#pragma mark - <  UICollectionViewDelegate >

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    PhotoCollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"PhotoCollectionViewCell" forIndexPath:indexPath];
//    PHAsset *phAsset = [self.fetchResult objectAtIndex:indexPath.row];
    
//    if ([[Preferences sharedInstance].deleteArray indexOfObject:phAsset.localIdentifier] != NSNotFound) {
//        [cell setIsDelete:YES];
//    } else {
//        [cell setIsDelete:NO];
//    }
    
    [self fetchImageWithIndex:indexPath.row withCompletionHandler:^(PHAsset *phAsset, UIImage *image) {
        [cell updateImage:image];
    }];
    
    return cell;
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath{
    CGSize cellSize = CGSizeMake(self.cellwidth, self.cellwidth);
    return cellSize;
}


// 两列cell之间的间距
- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section {
    return self.cellMargin;
}


- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section {
    return self.cellMargin;
}


- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.filteredAssets ? self.filteredAssets.count : self.fetchResult.count;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    UIStoryboard *storyBoard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    ScrollViewController *photoScrollViewController = [storyBoard instantiateViewControllerWithIdentifier:@"ScrollViewController"];

    // 如果有筛选数据，需要创建一个包含筛选后资产的PHFetchResult
    if (self.filteredAssets) {
        // 创建一个临时的PHFetchResult来传递筛选后的数据
        // 注意：这里我们传递原始的fetchResult，但在ScrollViewController中需要处理筛选逻辑
        photoScrollViewController.photoAssetArray = self.fetchResult;

        // 找到选中资产在原始数据中的索引
        PHAsset *selectedAsset = self.filteredAssets[indexPath.row];
        NSInteger originalIndex = 0;
        for (NSInteger i = 0; i < self.fetchResult.count; i++) {
            PHAsset *asset = [self.fetchResult objectAtIndex:i];
            if ([asset.localIdentifier isEqualToString:selectedAsset.localIdentifier]) {
                originalIndex = i;
                break;
            }
        }
        photoScrollViewController.startIndexPath = [NSIndexPath indexPathForRow:originalIndex inSection:0];
    } else {
        photoScrollViewController.photoAssetArray = self.fetchResult;
        photoScrollViewController.startIndexPath = indexPath;
    }

    [self.navigationController pushViewController:photoScrollViewController animated:YES];
}

- (IBAction)titleBtnClicked:(UIButton *)sender {
    UIStoryboard *storyBoard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    AlbumSelectViewController *albumSelectViewController = [storyBoard instantiateViewControllerWithIdentifier:@"AlbumSelectViewController"];
    albumSelectViewController.title = @"选择相簿";
//    AlbumSelectViewController *albumSelectViewController = albumselectnavigation.viewControllers.firstObject;
    albumSelectViewController.delegate = self;
    
    [self presentViewController:albumSelectViewController animated:YES completion:nil];
}


- (void)updateTitleButton {
    NSString *titleText;
    NSInteger count = self.filteredAssets ? self.filteredAssets.count : self.fetchResult.count;

    if (self.collection == nil) {
        titleText = [NSString stringWithFormat:@"%@（%ld）", @"最近项目", count];
    } else {
        titleText = [NSString stringWithFormat:@"%@（%ld）", self.collection.localizedTitle, count];
    }

    [self.titleBtn setTitle:titleText forState:UIControlStateNormal];
    [self.titleBtn setImage:[UIImage imageNamed:@"change"] forState:UIControlStateNormal];
    [self.titleBtn sizeToFit];
    [self.titleBtn horizontalCenterTitleAndImage:0];
}

- (void)setupBackButton {
    // 创建返回按钮
    self.backButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.backButton setImage:[UIImage imageNamed:@"icon_back"] forState:UIControlStateNormal];
    [self.backButton addTarget:self action:@selector(backButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    
    // 设置按钮位置
    self.backButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.backButton];
    
    // 添加约束 - 左上角位置
    [NSLayoutConstraint activateConstraints:@[
        [self.backButton.leadingAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.leadingAnchor constant:16],
//        [self.backButton.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor constant:12],
        [self.backButton.centerYAnchor constraintEqualToAnchor:self.titleBtn.centerYAnchor],
        [self.backButton.widthAnchor constraintEqualToConstant:44],
        [self.backButton.heightAnchor constraintEqualToConstant:32],
    ]];
}

- (void)backButtonTapped {
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)AlbumSelectViewControllerDidSelect:(PHAssetCollection *)collection andPhotoAsset:(PHFetchResult<PHAsset *> *)photoAssetArray andIndex:(NSInteger)index {
    // 更新当前的 collection 属性
    self.collection = collection;
    self.originalFetchResult = photoAssetArray;

    // 更新标题
    [self updateTitleButton];

    // 重新应用筛选
    [self applyFilter];

    [self.collectionView reloadData];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self.collectionView scrollToItemAtIndexPath:[NSIndexPath indexPathForRow:index inSection:0] atScrollPosition:UICollectionViewScrollPositionNone animated:NO];
    });
}

#pragma mark - Filter Bar Setup

- (void)setupFilterBar {
    // 创建筛选栏容器
    self.filterBarView = [[UIView alloc] init];
    self.filterBarView.backgroundColor = [UIColor colorWithRed:0.1 green:0.1 blue:0.1 alpha:1.0];
    self.filterBarView.layer.cornerRadius = 8;
    self.filterBarView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.filterBarView];

    // 创建未整理按钮
    self.unorganizedButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.unorganizedButton setTitle:@"未整理" forState:UIControlStateNormal];
    [self.unorganizedButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [self.unorganizedButton setTitleColor:[UIColor blackColor] forState:UIControlStateSelected];
    self.unorganizedButton.titleLabel.font = [UIFont systemFontOfSize:14];
    self.unorganizedButton.layer.cornerRadius = 6;
    self.unorganizedButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.unorganizedButton addTarget:self action:@selector(filterButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    self.unorganizedButton.tag = PhotoFilterTypeUnorganized;
    [self.filterBarView addSubview:self.unorganizedButton];

    // 创建已归档按钮
    self.archivedButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.archivedButton setTitle:@"已归档" forState:UIControlStateNormal];
    [self.archivedButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [self.archivedButton setTitleColor:[UIColor blackColor] forState:UIControlStateSelected];
    self.archivedButton.titleLabel.font = [UIFont systemFontOfSize:14];
    self.archivedButton.layer.cornerRadius = 6;
    self.archivedButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.archivedButton addTarget:self action:@selector(filterButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    self.archivedButton.tag = PhotoFilterTypeArchived;
    [self.filterBarView addSubview:self.archivedButton];

    // 设置约束
    [NSLayoutConstraint activateConstraints:@[
        // 筛选栏约束
        [self.filterBarView.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],
        [self.filterBarView.topAnchor constraintEqualToAnchor:self.titleBtn.bottomAnchor constant:8],
        [self.filterBarView.widthAnchor constraintEqualToAnchor:self.view.widthAnchor constant:-40],
        [self.filterBarView.heightAnchor constraintEqualToConstant:40],

        // 未整理按钮约束
        [self.unorganizedButton.leadingAnchor constraintEqualToAnchor:self.filterBarView.leadingAnchor constant:4],
        [self.unorganizedButton.topAnchor constraintEqualToAnchor:self.filterBarView.topAnchor constant:4],
        [self.unorganizedButton.bottomAnchor constraintEqualToAnchor:self.filterBarView.bottomAnchor constant:-4],
        [self.unorganizedButton.widthAnchor constraintEqualToAnchor:self.filterBarView.widthAnchor multiplier:0.5 constant:-20],

        // 已归档按钮约束
        [self.archivedButton.trailingAnchor constraintEqualToAnchor:self.filterBarView.trailingAnchor constant:-4],
        [self.archivedButton.topAnchor constraintEqualToAnchor:self.filterBarView.topAnchor constant:4],
        [self.archivedButton.bottomAnchor constraintEqualToAnchor:self.filterBarView.bottomAnchor constant:-4],
        [self.archivedButton.widthAnchor constraintEqualToAnchor:self.filterBarView.widthAnchor multiplier:0.5 constant:-20],
    ]];

    // 更新collectionView的约束，让它在筛选栏下方
    // 移除原有的top约束
    NSLayoutConstraint *topConstraintToRemove = nil;
    for (NSLayoutConstraint *constraint in self.view.constraints) {
        if ((constraint.firstItem == self.collectionView && constraint.firstAttribute == NSLayoutAttributeTop) ||
            (constraint.secondItem == self.collectionView && constraint.secondAttribute == NSLayoutAttributeTop)) {
            topConstraintToRemove = constraint;
            break;
        }
    }
    if (topConstraintToRemove) {
        [self.view removeConstraint:topConstraintToRemove];
    }

    // 添加新的约束，让collectionView在筛选栏下方
    [NSLayoutConstraint activateConstraints:@[
        [self.collectionView.topAnchor constraintEqualToAnchor:self.filterBarView.bottomAnchor constant:8]
    ]];

    // 设置初始状态
    [self updateFilterButtonsAppearance];
}

- (void)filterButtonTapped:(UIButton *)sender {
    PhotoFilterType newFilterType = (PhotoFilterType)sender.tag;
    if (newFilterType != self.currentFilterType) {
        self.currentFilterType = newFilterType;
        [self updateFilterButtonsAppearance];
        [self applyFilter];
        [self.collectionView reloadData];
    }
}

- (void)updateFilterButtonsAppearance {
    // 重置所有按钮状态
    self.unorganizedButton.selected = NO;
    self.unorganizedButton.backgroundColor = [UIColor clearColor];
    self.archivedButton.selected = NO;
    self.archivedButton.backgroundColor = [UIColor clearColor];

    // 设置当前选中按钮的状态
    UIButton *selectedButton = (self.currentFilterType == PhotoFilterTypeUnorganized) ? self.unorganizedButton : self.archivedButton;
    selectedButton.selected = YES;
    selectedButton.backgroundColor = [UIColor whiteColor];
}

- (void)applyFilter {
    if (!self.originalFetchResult) {
        return;
    }

    NSArray *archiveArray = [Preferences sharedInstance].archiveArray ?: @[];
    NSArray *deleteArray = [Preferences sharedInstance].deleteArray ?: @[];
    
    NSMutableArray *filteredAssets = [NSMutableArray array];

    for (NSInteger i = 0; i < self.originalFetchResult.count; i++) {
        PHAsset *asset = [self.originalFetchResult objectAtIndex:i];
        BOOL isArchived = [archiveArray containsObject:asset.localIdentifier];
        BOOL isDelete = [deleteArray containsObject:asset.localIdentifier];
        if (!isDelete) {
            if ((self.currentFilterType == PhotoFilterTypeUnorganized && !isArchived) ||
                (self.currentFilterType == PhotoFilterTypeArchived && isArchived)) {
                [filteredAssets addObject:asset];
            }
        }
    }

    // 保存筛选后的资产数组
    self.filteredAssets = filteredAssets;

    // 更新fetchResult以便其他方法使用
    self.fetchResult = self.originalFetchResult;

    // 更新标题显示筛选后的数量
    [self updateTitleButton];
}

@end
