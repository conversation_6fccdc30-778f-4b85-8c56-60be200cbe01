//
//  PhotoSelectViewController.h
//  photoclear
//
//  Created by lifubing on 2021/6/13.
//

#import "BaseViewController.h"
#import <Photos/Photos.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, PhotoFilterType) {
    PhotoFilterTypeUnorganized = 0,  // 未整理
    PhotoFilterTypeArchived = 1      // 已归档
};

@protocol PhotoSelectViewControllerDelegate <NSObject>
- (void)PhotoSelectViewControllerDidSelect:(PHAssetCollection *)collection andPhotoAsset:(PHFetchResult<PHAsset *> *)photoAssetArray andIndex:(NSInteger)index;
@end


@interface PhotoSelectViewController : BaseViewController
@property (nonatomic, strong) PHFetchResult *fetchResult;
@property (nonatomic, strong) PHAssetCollection *collection;
@property (nonatomic, weak) id<PhotoSelectViewControllerDelegate> delegate;

// 筛选相关属性
@property (nonatomic, strong) UIView *filterBarView;
@property (nonatomic, strong) UIButton *unorganizedButton;
@property (nonatomic, strong) UIButton *archivedButton;
@property (nonatomic, assign) PhotoFilterType currentFilterType;
@property (nonatomic, strong) PHFetchResult *originalFetchResult;  // 保存原始数据
@property (nonatomic, strong) NSMutableArray<PHAsset *> *filteredAssets;  // 筛选后的资产数组
@end

NS_ASSUME_NONNULL_END
