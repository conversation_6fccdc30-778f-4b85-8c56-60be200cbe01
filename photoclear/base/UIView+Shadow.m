//
//  UIView+Shadow.m
//  videorec
//
//  Created by <PERSON>,Fubing on 2018/6/20.
//  Copyright © 2018年 baidu. All rights reserved.
//

#import "UIView+Shadow.h"

@implementation UIView (Shadow)

- (void)dropShadowWithoffset:(CGSize)offset
                    radius:(CGFloat)radius
                     color:(UIColor *)color
                   opacity:(CGFloat)opacity {
    
    // Creating shadow path for better performance
    CGMutablePathRef path = CGPathCreateMutable();
    CGPathAddRect(path, NULL, self.bounds);
    self.layer.shadowPath = path;
    CGPathCloseSubpath(path);
    CGPathRelease(path);
    
    self.layer.shadowColor = color.CGColor;
    //阴影偏移度向右0 向下0.5
    self.layer.shadowOffset = offset;
    //阴影半径
    self.layer.shadowRadius = radius;
    //阴影透明度
    self.layer.shadowOpacity = opacity;
    self.clipsToBounds = NO;
//    [self.superview bringSubviewToFront:self];
}

- (void)dropShaowNomal {
    [self dropShadowWithoffset:CGSizeMake(0, 0) radius:5 color:[UIColor colorWithRed:0.24 green:0.27 blue:0.31 alpha:0.5]
                       opacity:0.7];
}

- (void)dropLayerShaowWithColor:(UIColor *)color {
    self.layer.shadowColor = [color CGColor];
    self.layer.shadowOpacity = 0.3;
    self.layer.shadowRadius = 5;
    self.layer.shadowOffset = CGSizeMake(0.0f, 0.0f);
}

- (void)dropLayerShaowWithColor:(UIColor *)color opacity:(CGFloat)opacity {
    self.layer.shadowColor = [color CGColor];
    self.layer.shadowOpacity = opacity;
    self.layer.shadowRadius = 5;
    self.layer.shadowOffset = CGSizeMake(0.0f, 0.0f);
}

- (void)dropLayerShaowNomal {
    [self dropLayerShaowWithColor:[UIColor colorWithRed:0.24 green:0.27 blue:0.31 alpha:0.5]];
}

+ (UIImage *)snapshotWithView:(UIView *)view
{
    CGSize size = view.bounds.size;
    UIGraphicsBeginImageContextWithOptions(size, YES, 0);
    [view drawViewHierarchyInRect:view.bounds afterScreenUpdates:YES];
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return image;
}

- (void)dropLayerBorderNomal {
    self.layer.borderColor = [UIColor blackColor].CGColor;
    self.layer.borderWidth = 1;
}

@end
