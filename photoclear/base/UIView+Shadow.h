//
//  UIView+Shadow.h
//  videorec
//
//  Created by <PERSON>,Fubing on 2018/6/20.
//  Copyright © 2018年 baidu. All rights reserved.
//

#import <UIKit/UIKit.h>

@interface UIView (Shadow)

- (void)dropShadowWithoffset:(CGSize)offset
                      radius:(CGFloat)radius
                       color:(UIColor *)color
                     opacity:(CGFloat)opacity;

+ (UIImage *)snapshotWithView:(UIView *)view;

- (void)dropShaowNomal;

- (void)dropLayerShaowNomal;
- (void)dropLayerShaowWithColor:(UIColor *)color;
- (void)dropLayerShaowWithColor:(UIColor *)color opacity:(CGFloat)opacity;

- (void)dropLayerBorderNomal;

@end
